{"name": "i2i-frontend", "version": "0.1.0", "private": true, "engines": {"node": "^20.9.0", "yarn": "^1.22.18"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "start:ssl": "NODE_ENV=development node server.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write '**/*.{js,ts,tsx,json,css,scss}'", "test": "NODE_OPTIONS=\"$NODE_OPTIONS --experimental-vm-modules\" jest", "test:watch": "NODE_OPTIONS=\"$NODE_OPTIONS --experimental-vm-modules\" jest --watch", "test:coverage": "NODE_OPTIONS=\"$NODE_OPTIONS --experimental-vm-modules\" jest --coverage"}, "dependencies": {"@faker-js/faker": "9.2.0", "@heroicons/react": "2.2.0", "@heroui/react": "2.6.14", "@monaco-editor/react": "4.6.0", "@tanstack/react-query": "5.61.0", "@types/dompurify": "3.2.0", "@types/react-csv": "1.1.10", "axios": "1.7.7", "axios-mock-adapter": "2.1.0", "classnames": "2.5.1", "docx": "9.0.3", "formidable": "3.5.4", "framer-motion": "11.11.17", "jsonwebtoken": "9.0.2", "loglevel": "1.9.2", "markdown-it": "14.1.0", "marked": "15.0.2", "mermaid": "11.4.0", "monaco-editor": "0.52.0", "monaco-mermaid": "1.1.0", "next": "15.0.3", "next-auth": "4.24.10", "next-intl": "3.25.1", "puppeteer": "23.9.0", "react": "18", "react-csv": "2.2.2", "react-dom": "18", "react-hook-form": "7.53.2", "react-markdown": "9.0.1", "react-markdown-editor-lite": "1.3.4", "react-split": "2.0.14", "react-syntax-highlighter": "15.6.1", "react-toastify": "10.0.6", "remark-gfm": "4.0.0", "zod": "3.23.8"}, "devDependencies": {"@eslint/eslintrc": "3.2.0", "@eslint/js": "9.15.0", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.0.1", "@types/jest": "29.5.14", "@types/jsonwebtoken": "9.0.7", "@types/markdown-it": "14.1.2", "@types/node": "22", "@types/react": "18", "@types/react-dom": "18", "@types/react-syntax-highlighter": "15.5.13", "@types/testing-library__react-hooks": "4.0.0", "@typescript-eslint/eslint-plugin": "8.15.0", "@typescript-eslint/parser": "8.15.0", "eslint": "9.15.0", "eslint-config-next": "15.0.3", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-react": "7.37.2", "globals": "15.12.0", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "postcss": "8", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "0.6.9", "tailwindcss": "3.4.15", "ts-node": "10.9.2", "typescript": "5", "typescript-eslint": "8.15.0"}}